package eu.torvian.chatbot.server.service.error.message

/**
 * Represents possible errors during the process of receiving and responding to a new user message.
 */
sealed interface ProcessNewMessageError {
    /**
     * Indicates that the session the message belongs to was not found.
     * Maps from SessionError.SessionNotFound in the DAO layer.
     */
    data class SessionNotFound(val sessionId: Long) : ProcessNewMessageError
    /**
     * Indicates that the parent message specified for a reply was not found.
     * Maps from MessageError.MessageNotFound in the DAO layer.
     */
    data class ParentMessageNotFound(val parentId: Long) : ProcessNewMessageError
    /**
     * Indicates that the parent message does not belong to the specified session (business logic validation).
     */
    data class InvalidParentInSession(val sessionId: Long, val parentId: Long) : ProcessNewMessageError
    /**
     * Indicates that a provided ID for a related entity (e.g., session, parent message)
     * does not exist in the database, detected via foreign key constraint.
     * Maps from MessageError.ForeignKeyViolation in the DAO layer.
     */
    data class InvalidRelatedEntity(val message: String) : ProcessNewMessageError
    /**
     * Indicates that the session's current model or settings are not configured correctly or not found.
     * Maps from ModelError.ModelNotFound, SettingsError.SettingsNotFound, or business logic check.
     */
    data class ModelConfigurationError(val message: String) : ProcessNewMessageError
    /**
     * Indicates a failure occurred when calling the external LLM API.
     * Wraps the external service error details.
     * Maps from exceptions or specific error responses from LLMApiClient.
     */
    data class ExternalServiceError(val message: String) : ProcessNewMessageError
}
