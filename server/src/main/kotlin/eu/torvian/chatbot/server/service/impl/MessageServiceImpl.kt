package eu.torvian.chatbot.server.service.impl
//import arrow.core.*
//import arrow.core.raise.*
//
//import eu.torvian.chatbot.common.models.ChatMessage
//import eu.torvian.chatbot.common.models.LLMModel
//import eu.torvian.chatbot.common.models.ModelSettings
//import eu.torvian.chatbot.server.data.dao.MessageDao
//import eu.torvian.chatbot.server.data.dao.SessionDao
//import eu.torvian.chatbot.server.data.dao.error.*
//
//import eu.torvian.chatbot.server.external.llm.LLMApiClient
//import eu.torvian.chatbot.server.external.models.OpenAiApiModels.ChatCompletionResponse
//import eu.torvian.chatbot.server.service.MessageService
//import eu.torvian.chatbot.server.service.ModelService
//import eu.torvian.chatbot.server.service.error.message.*
//import eu.torvian.chatbot.server.service.error.model.*
//import eu.torvian.chatbot.server.service.error.settings.*
//import eu.torvian.chatbot.server.service.security.CredentialManager
//import eu.torvian.chatbot.server.service.security.error.CredentialError
//
//import eu.torvian.chatbot.server.utils.transactions.TransactionScope
//
///**
// * Implementation of the [MessageService] interface.
// * Handles the business logic for processing and managing chat messages.
// * Uses a [TransactionScope] and Arrow Raise DSL for atomic operations and error handling.
// */
//class MessageServiceImpl(
//    private val messageDao: MessageDao,
//    private val sessionDao: SessionDao,
//    private val modelService: ModelService,
//    private val llmApiClient: LLMApiClient,
//    private val credentialManager: CredentialManager,
//    private val transactionScope: TransactionScope,
//) : MessageService {
//
//    override suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> {
//        return transactionScope.transaction {
//            messageDao.getMessagesBySessionId(sessionId)
//        }
//    }
//
//    override suspend fun processNewMessage(sessionId: Long, content: String, parentMessageId: Long?): Either<ProcessNewMessageError, List<ChatMessage>> =
//        transactionScope.transaction {
//            either {
//
//                // 1. Validate session
//                val session = withError({ daoError: SessionError.SessionNotFound ->
//                    ProcessNewMessageError.SessionNotFound(daoError.id)
//                }) {
//                    sessionDao.getSessionById(sessionId).bind()
//                }
//
//                // 2. Validate parent message (if any)
//                if (parentMessageId != null) {
//                    withError({ daoError: MessageError.MessageNotFound ->
//                        ProcessNewMessageError.ParentMessageNotFound(daoError.id)
//                    }) {
//                        messageDao.getMessageById(parentMessageId).bind()
//                    }.also { parentMessage ->
//                        if (parentMessage.sessionId != sessionId) {
//                            raise(ProcessNewMessageError.InvalidParentInSession(sessionId, parentMessageId))
//                        }
//                    }
//                }
//
//                // 3. Save user message
//                val userMessage = withError({ daoError: MessageError.ForeignKeyViolation ->
//                    ProcessNewMessageError.InvalidRelatedEntity("Failed to link user message: ${daoError.message}")
//                }) {
//                    messageDao.insertUserMessage(sessionId, content, parentMessageId).bind()
//                }
//
//                // 4. Update parent's children list (if any)
//                if (parentMessageId != null) {
//                    withError({ daoError: MessageAddChildError ->
//                        when(daoError) {
//                            is MessageAddChildError.ParentNotFound -> ProcessNewMessageError.ParentMessageNotFound(daoError.parentId)
//                            is MessageAddChildError.ChildAlreadyExists -> ProcessNewMessageError.InvalidInput("Child message ${daoError.childId} already exists for parent ${daoError.parentId}")
//                        }
//                    }) {
//                        messageDao.addChildToMessage(parentMessageId, userMessage.id).bind()
//                    }
//                }
//
//                // 5. Get model and settings config
//                val modelId = session.currentModelId ?: raise(ProcessNewMessageError.ModelConfigurationError("No model selected for session"))
//                val settingsId = session.currentSettingsId ?: raise(ProcessNewMessageError.ModelConfigurationError("No settings selected for session"))
//
//                // Fetch Model
//                val model = withError({ serviceError: GetModelError.ModelNotFound ->
//                    ProcessNewMessageError.ModelConfigurationError("Model with ID ${serviceError.id} not found")
//                }) {
//                    modelService.getModelById(modelId).bind()
//                }
//
//                // Fetch Settings
//                val settings = withError({ serviceError: GetSettingsByIdError ->
//                    when(serviceError) {
//                        is GetSettingsByIdError.SettingsNotFound -> ProcessNewMessageError.ModelConfigurationError("Settings with ID ${serviceError.id} not found")
//                    }
//                }) {
//                    modelService.getSettingsById(settingsId).bind()
//                }
//
//                // Get API Key
//                val apiKey: String? = model.apiKeyId?.let { keyId ->
//                    withError({ credError: CredentialError.CredentialNotFound ->
//                        ProcessNewMessageError.ModelConfigurationError("API key not found in secure storage for model ID ${model.id} (key alias: $keyId)")
//                    }) {
//                        credentialManager.getCredential(keyId).bind()
//                    }
//                }
//
//                // 6. Build context
//                val allMessagesInSession = messageDao.getMessagesBySessionId(sessionId)
//                val context = buildContext(userMessage, allMessagesInSession)
//
//                // 7. Call LLM API (stubbed for S1)
//                val llmResponse = withError({ externalErrorMsg: String ->
//                    ProcessNewMessageError.ExternalServiceError("LLM API Error: $externalErrorMsg")
//                }) {
//                    llmApiClient.completeChat(
//                        messages = context,
//                        modelConfig = model,
//                        settings = settings,
//                        apiKey = apiKey
//                    ).bind()
//                }
//
//                // 8. Save assistant message
//                val assistantMessageContent = llmResponse.choices.firstOrNull()?.message?.content ?: "Error: Empty LLM response"
//                val assistantMessage = withError({ daoError: MessageError.ForeignKeyViolation ->
//                    ProcessNewMessageError.InvalidRelatedEntity("Failed to link assistant message: ${daoError.message}")
//                }) {
//                    messageDao.insertAssistantMessage(
//                        sessionId,
//                        assistantMessageContent,
//                        userMessage.id,
//                        modelId,
//                        settingsId
//                    ).bind()
//                }
//
//                // 9. Update user message's children list
//                withError({ daoError: MessageAddChildError ->
//                    when(daoError) {
//                        is MessageAddChildError.ParentNotFound -> ProcessNewMessageError.ParentMessageNotFound(daoError.parentId)
//                        is MessageAddChildError.ChildAlreadyExists -> ProcessNewMessageError.InvalidInput("Child message ${daoError.childId} already exists for parent ${daoError.parentId}")
//                    }
//                }) {
//                    messageDao.addChildToMessage(userMessage.id, assistantMessage.id).bind()
//                }
//
//                // 10. Update session's leaf message ID
//                withError({ daoError: SessionError ->
//                    when(daoError) {
//                        is SessionError.SessionNotFound -> ProcessNewMessageError.SessionNotFound(daoError.id)
//                        is SessionError.ForeignKeyViolation -> ProcessNewMessageError.InvalidInput("Failed to update session leaf message ID: ${daoError.message}")
//                    }
//                }) {
//                    sessionDao.updateSessionLeafMessageId(sessionId, assistantMessage.id).bind() // .bind() handles Either<SessionError, Unit>
//                }
//
//                // 11. Return new messages as the success value
//                listOf(userMessage, assistantMessage)
//            }
//        }
//
//    private fun buildContext(currentUserMessage: ChatMessage, allMessages: List<ChatMessage>): List<ChatMessage> {
//        // Simplified context building for Sprint 1 stub - keeps thread branch
//        val context = mutableListOf<ChatMessage>()
//        val messageMap = allMessages.associateBy { it.id }
//        var c: ChatMessage? = currentUserMessage
//        while (c != null) {
//            context.add(0, c)
//            c = c.parentMessageId?.let { messageMap[it] }
//        }
//        return context
//    }
//
//    override suspend fun updateMessageContent(id: Long, content: String): Either<UpdateMessageContentError, ChatMessage> =
//        transactionScope.transaction {
//            either {
//                withError({ daoError: MessageError.MessageNotFound ->
//                    UpdateMessageContentError.MessageNotFound(daoError.id)
//                }) {
//                    messageDao.updateMessageContent(id, content).bind()
//                }
//            }
//        }
//
//    override suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit> =
//        transactionScope.transaction {
//            either {
//                withError({ daoError: MessageError.MessageNotFound ->
//                    DeleteMessageError.MessageNotFound(daoError.id)
//                }) {
//                    messageDao.deleteMessage(id).bind()
//                }
//            }
//        }
//}